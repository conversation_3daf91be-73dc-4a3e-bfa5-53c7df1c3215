<?php

namespace App\Console\Commands;

use App\Models\Channel;
use App\Models\ChannelSupplier;
use Illuminate\Console\Command;
use App\Services\ChannelPidLimitService;
use Illuminate\Support\Carbon;

class TempChannelLimitUpdate extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:tempChannelLimitUpdate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);
        // 开始时打印详细日志
        $this->info("账单标识和PID交易计数更新任务开始");

        //获取需要进行更新的渠道
        $channelList = ChannelSupplier::select('id', 'timezone')->get();
        // 循环渠道列表根据timezone获取对应时间判断是否是在00:00到01:00之间 如果timezone为空则默认为app配置里的时区
        $channels = [];
        $channelList->map(function ($item) use (&$channels) {
            $timezone  = \config('app.timezone');

            if ($item->timezone) {
                // 使用timezone前需要判断当前app配置里的时区时间是否大于effective_date时间
                $effectiveDate = Carbon::parse($item->effective_date);
                if (!$effectiveDate->gt(\now())) {
                    $timezone = $item->timezone;
                }
            }

            $channels[$timezone][] = $item->id;
        });

        // 如果没有可以结束运行了
        if (empty($channels)) {
            $this->info("账单标识和PID交易计数更新任务结束");
            return;
        }

        // 循环channels根据时间zone获取对应渠道数据
        foreach ($channels as $timezone => $channelIds){
            // 使用channel_supplier_id查询channel表取channel字段
            $channelIds = Channel::whereIn('channel_supplier_id', $channels)->pluck('channel')->toArray();

            // 获取渠道交易数据
            $orderSettlementData = ChannelPidLimitService::getLimitData($timezone, \config('app.timezone'), $channelIds);
            ChannelPidLimitService::updateCache($orderSettlementData,$timezone);
        }

        // 结束时打印详细日志 记录更新渠道以及数量
        $this->info("账单标识和PID交易计数更新任务结束");
    }

}
