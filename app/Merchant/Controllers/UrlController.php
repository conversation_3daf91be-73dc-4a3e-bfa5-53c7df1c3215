<?php

namespace App\Merchant\Controllers;

use App\Admin\Repositories\MerchantUrl;
use App\Merchant\Actions\Grid\RevokeUrl;
use App\Models\MerchantBusiness;
use App\Models\MerchantKyc as MerchantKycModel;
use App\Models\MerchantUrl as MerchantUrlModel;
use App\Models\RiskCase;
use App\Services\RiskCasesService;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tooltip;
use Illuminate\Support\Facades\Auth;
use App\Services\UrlService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UrlController extends AdminController
{
    public function title()
    {
        return admin_trans_label('网址管理');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(MerchantUrlModel::with(['brand', 'mcc', 'case'])->where('merchant_id', Auth::user()->merchant_id)->where('url_status', '<>', MerchantUrlModel::STATUS_DELETE)->orderByDesc('updated_at'), function (Grid $grid) {
            $grid->column('business_id');
            $grid->column('url_name');
            $grid->column('main_url_name');
            $grid->column('website_mode')->display(function ($val) {
                return admin_trans_option(MerchantUrlModel::$merchantWebsiteModeMap[$val] ?? admin_trans_field('未知'), 'website_mode');
            });
            $grid->column('saas_url_name');
            $grid->column('mcc.name', 'MCC');
            $grid->column('brand.name', admin_trans_field('brand_name'));
            $grid->column('url_status')->display(function ($val) {
                $tip = '';

                if ($val == MerchantUrlModel::STATUS_DOWN || $val == MerchantUrlModel::STATUS_REJECT) {
                    Tooltip::make('#' . $this->id)->title($this->status_remarks);
                    $tip = "<i id='{$this->id}' class='feather icon-help-circle'></i>";
                }

                return $tip . admin_trans_option(MerchantUrlModel::$merchantStatusMap[$val] ?? admin_trans_field('未知'), 'url_status');
            });
            $grid->column('remarks');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->column('action')->display(function () {
                if ($this->url_status == MerchantUrlModel::STATUS_CHECK && ($this->case && $this->case->case_status == RiskCase::CASE_STATUS_UNDER)) {
                    return new RevokeUrl($this->id);
                }

                if ($this->url_status == MerchantUrlModel::STATUS_REJECT) {
                    Form::dialog(admin_trans_field('修改'))
                        ->click('.m-url-edit')
                        ->width('40%')
                        ->height('75%')
                        ->success('Dcat.reload()');

                    return "<span class='btn-success btn btn-primary btn-shadow m-url-edit' data-url=" . admin_route('m.urls.create', ['id' => $this->id]) . ">" . admin_trans_field('修改') . "</span>&nbsp;";
                }
            });

            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('url_name')->select(MerchantUrlModel::where('merchant_id', Auth::user()->merchant_id)->get()->pluck('url_name', 'url_name')->toArray());
                $filter->equal('website_mode')->select(admin_trans_label('website_mode'));
                $filter->equal('business_id')->select(MerchantBusiness::where('merchant_id', Auth::user()->merchant_id)->get()->pluck('business_id', 'business_id')->toArray());
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new MerchantUrl(), function (Show $show) {
            $show->merchant_id;
            $show->merchant_name;
            $show->business_id;
            $show->url_name;
            $show->d_mcc_id;
            $show->d_brand_id;
            $show->cc_types;
            $show->url_status;
            $show->pid_status;
            $show->remarks;
            $show->created_at;
            $show->updated_at;
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $merchantId   = Auth::user()->merchant_id;
        $businessList = MerchantBusiness::where('merchant_id', $merchantId)->get()->pluck('business_id', 'business_id')->toArray();

        return Form::make(new MerchantUrl(), function (Form $form) use ($businessList) {
            $form->hidden('id');
            $form->hidden('risk_case_id');
            $form->hidden('merchant_id');
            $form->hidden('merchant_name');
            $form->hidden('url_status');
            $form->hidden('main_url_name');
            $form->hidden('saas_url_name');
            $form->select('business_id')->options($businessList)->rules("required");
            $form->text('url_name')->rules("required|string|max:128");
            $form->select('website_mode')->options(admin_trans_label('website_mode'))->rules("required")
                 ->when(1, function (Form $form) {
                     $form->html(function () use ($form) {
                         $saas_url_name = $form->model()->saas_url_name ?? '';
                         if (!empty($saas_url_name)) {
                             // saas网址格式处理
                             $saas_url_name = substr_replace(substr_replace(trim($saas_url_name), '', 0, 8), '', -15);
                         }

                         return '<div class="input-group">
                            <span class="input-group-prepend"><span class="input-group-text bg-white">https://</span></span>
                            <input type="text" name="saas_url_name" value="' . $saas_url_name . '" class="form-control">
                            <span class="input-group-prepend"><span class="input-group-text bg-white">.myshoplaza.com</span></span>
                        </div>';
                     }, admin_trans_field('saas_url_name'));
                 });
            $form->text('remarks');

            // 如果有 id 说明是编辑
            if (request()->input('id')){
                $url = MerchantUrlModel::where('id', request()->input('id'))->first();
                $form->model($url->toArray());
                $form->fillFields($url->toarray());
            }
        })->saving(function (Form $form) {
            $businessData        = MerchantBusiness::find($form->business_id);
            $form->merchant_id   = $businessData->merchant_id;
            $form->merchant_name = $businessData->merchant_name;
            $form->url_status    = MerchantUrlModel::STATUS_CHECK;
            $formId              = $form->input('id') ?? null;
            $businessId          = $form->input('business_id');

            if ($form->website_mode == MerchantUrlModel::WEBSITE_MODE_SHOPLAZZA) {
                $saas_url_name = $form->saas_url_name ?? '';
                if (empty($saas_url_name)) {
                    return $form->response()->error('SaaS网址名称不能为空！');
                }

                // 拼接完整店匠网址
                $saas_url_name = $form->saas_url_name = 'https://' . $form->saas_url_name . '.myshoplaza.com';
                // SaaS网址为主域名，要判断网址名称是否重复
                if (MerchantUrlModel::where('id', '<>', $formId)->where('url_status', '<>', MerchantUrlModel::STATUS_DELETE)->where(['url_name' => $form->url_name, 'business_id' => $businessId])->exists()) {
                    return $form->response()->error('网址名称已存在！');
                }

                $mainUrlName = UrlService::_getMainUrlName($saas_url_name);
            } else {
                // 建站方式不是店匠，把SaaS网址清空
                $form->saas_url_name = '';
                $mainUrlName         = UrlService::_getMainUrlName($form->url_name);
            }

            $validator = Validator::make(['main_url_name' => $mainUrlName], [
                'main_url_name' => [
                    'required', 'string', 'max:128',
                    Rule::unique('merchant_urls')->where(function ($query) use ($businessId) {
                        return $query->where('business_id', $businessId)->where('url_status', '<>', MerchantUrlModel::STATUS_DELETE);
                    })->ignore($formId)
                ],
            ]);

            if ($validator->fails()) {
                return $form->response()->error($validator->errors()->first());
            }

            $form->main_url_name = $mainUrlName;

            $kyc = MerchantKycModel::query()->where('merchant_id', $businessData->merchant_id)
                ->orderBy('id', 'desc')
                ->first();

            $caseService = new RiskCasesService();
            $case        = $caseService->addCases([
                                                      'merchant_id'   => $form->input('merchant_id'),
                                                      'merchant_name' => $form->input('merchant_name'),
                                                      'case_type'     => RiskCase::CASE_TYPE_CSC,
                                                      'country'       => $kyc->type ?? -1
                                                  ]);

            $form->risk_case_id = $case->id;

            if ($form->isCreating() && $form->id){
                // 将原本的 url 状态更新为删除
                MerchantUrlModel::where('id', $form->id)->update(['url_status' => MerchantUrlModel::STATUS_DELETE]);
            }

            $form->deleteInput('id');
        });
    }
}
