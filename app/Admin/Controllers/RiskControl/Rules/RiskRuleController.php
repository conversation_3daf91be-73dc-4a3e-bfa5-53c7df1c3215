<?php

namespace app\Admin\Controllers\RiskControl\Rules;

use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Tab;
use Dcat\Admin\Grid;

class RiskRuleController extends AdminController
{
    protected $title = '风险规则管理';

    // 标签页标题映射
    protected $tabTitles = [
        'transaction' => '交易规则',
        'card'        => '卡片规则',
        'ip'          => 'IP规则',
        'list'        => '名单规则',
        'merchant'    => '商户规则',
    ];

    protected $rules = [
        'transaction' => [
            ['title' => '日/月交易金额限额', 'description' => '商户配置了 day_money_astrict 或 month_money_astrict,当前订单金额加上历史交易金额超过限制时触发'],
            ['title' => '单笔交易金额限制', 'description' => '商户配置了 single_money_astrict 当前订单金额超过限制时触发'],
            ['title' => 'BID同卡号成功笔数限制', 'description' => '商户配置了 time_limits同卡号的当前订单加上历史交易次数超过限制时触发'],
            ['title' => 'CVV限制', 'description' => 'A卡J卡CVV长度不符合要求(等于3)'],
            ['title' => '商户订单号重复处理', 'description' => '同一商户订单号已存在成功订单或短时间内重复提交 10s内的同卡同金额同网站的连续支付 同卡同金额同网站120s以内有成功交易, 拦截支付'],
            ['title' => '退款合理性验证', 'description' => '退款金额不符合要求或退款时间不符合要求'],
            ['title' => '退款次数限制', 'description' => '当天退款次数超过限制'],
            ['title' => 'PID和账单标识超限剔除', 'description' => '账单标识单卡单笔限额  日，月限额 PID限额'],
        ],
        'card'        => [
            ['title' => '同卡号短时间频繁请求支付的限制', 'description' => '同一卡号在短时间内多次请求支付'],
            ['title' => '同卡号24小时内失败限制', 'description' => '同一卡号在短时间内多次请求支付'],
            ['title' => '卡有效期验证', 'description' => '卡有效期已过期或无效'],
            ['title' => '卡BIN验证', 'description' => '卡BIN信息无效或不匹配'],
            ['title' => '剔除卡，渠道24小时不重复', 'description' => '同卡同渠道  24小时内不重复'],
        ],
        'ip'          => [
            ['title' => 'IP黑名单', 'description' => '当前请求的  IP 地址在 China'],
            ['title' => '排除卡属国为中国的交易', 'description' => '卡属国为 China'],
            ['title' => '邮编校验', 'description' => '账单或收件地址邮编无效'],
        ],
        'list'        => [
            ['title' => '渠道黑名单验证', 'description' => '命中商户黑名单'],
            ['title' => '渠道白名单验证', 'description' => '渠道白名单验证不通过 and 交易卡种是白名单验证卡种'],
            ['title' => '卡号白名单验证', 'description' => '卡号不在白名单中'],
        ],
        'merchant'    => [
            ['title' => '邮箱后缀为@mail.ru', 'description' => '特殊商户拦截邮箱后缀@mail.ru'],
        ],
    ];

    /**
     * Make a grid builder.
     *
     * @return Tab
     */
    protected function grid(): Tab
    {
        $tab = new Tab();

        foreach ($this->rules as $key => $value) {
            $tabTitle = $this->tabTitles[$key] ?? $key;
            $tab->add($tabTitle, $this->tab($value));
        }

        return $tab->withCard();
    }

    protected function tab($data): Grid
    {
        // 将数组数据转换为 Collection，并添加索引作为 ID
        $collection = collect($data)->map(function ($item, $index) {
            return array_merge($item, ['id' => $index + 1]);
        });

        return Grid::make($collection, function (Grid $grid) {
            // 禁用不需要的功能
            $grid->disableActions();
            $grid->disableRowSelector();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disablePagination();

            // 定义表格列
            $grid->column('id', 'ID')->sortable();
            $grid->column('title', '规则名称')->sortable();
            $grid->column('description', '规则描述')->limit(100);

            // 添加快速搜索
            $grid->quickSearch(['title', 'description']);

            // 添加筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '规则名称');
                $filter->like('description', '规则描述');
            });
        });
    }
}
