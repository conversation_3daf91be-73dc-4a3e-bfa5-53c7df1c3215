<?php

use Dcat\Admin\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Mosiboom\DcatIframeTab\Controllers\IframeController;

Admin::routes();

Route::group([
                 'prefix'     => config('admin.route.prefix'),
                 'namespace'  => config('admin.route.namespace'),
                 'middleware' => ['web'],
             ], function (Router $router) {
    $router->get('/get_card/{status?}', 'Merchant\MerchantCardController@getCard');
    $router->get('/businesses/get_businesses/{internal_status?}', 'Merchant\BusinessController@getBusiness');
    $router->get('/open_im/get_login_address', 'OpenIm\OpenImController@getLoginAddress');
    $router->any('/settlement/deposit_info/{type}', 'Settlement\MerchantRegularDepositController@getDepositInfo');
    $router->get('/merchant_kyc_info/{mid}', 'Merchant\MerchantKycController@getKycByMid');
    $router->get('/channel_merchant/{mci}', 'Channel\ChannelMerchantController@getChannelMerchant');
    $router->get('/get_business', 'Channel\ChannelMerchantController@getBusiness');
    $router->get('/card_cipher_whitelist/get_card_info', 'RiskControl\Card\CardCipherWhitelistController@getCardInfo');
});

Route::group([
                 'prefix'     => config('admin.route.prefix'),
                 'namespace'  => config('admin.route.namespace'),
                 'middleware' => ['web', 'admin'],
             ], function (Router $router) {
    $router->get('/2fa/{mark}/verificationPage/{mode?}', 'SecondaryValidationController@verificationPage')->name('2fa.verificationPage');
    $router->post('/2fa/validateToken', 'SecondaryValidationController@validateToken')->name('2fa.validateToken');
    $router->post('/2fa/changePassword', 'SecondaryValidationController@changePassword')->name('2fa.changePassword');
    $router->get('/merchants/get_alarm_content/{merchant_id?}', 'Merchant\MerchantController@getAlarmContent');
});

Route::group([
                 'prefix'     => config('admin.route.prefix'),
                 'namespace'  => config('admin.route.namespace'),
                 'middleware' => config('admin.route.middleware'),
             ], function (Router $router) {
    $router->get('/dashboard', 'HomeController@index');
    // 系统
    $router->resource('/auth/audit_log', 'System\AuditLogController');

    // 参数管理
    $router->resource('/directory_countries', 'Directory\CountryController');
    $router->resource('/directory_currencies', 'Directory\CurrencyController');
    $router->resource('/directory_ccs', 'Directory\CcController');
    $router->resource('/directory_regions', 'Directory\RegionController');
    $router->resource('/directory_dictionaries', 'Directory\DictionaryController');
    $router->resource('/directory_external_codes', 'Directory\ExternalCodeController');
    $router->resource('/directory_carriers', 'Directory\CarrierController');
    $router->resource('/directory_chargeback_codes', 'Directory\ChargebackCodeController');
    $router->resource('/directory_order_uprates', 'Directory\OrderUprateController');
    $router->resource('/directory_order_uprates/{scheme_name}/directory_order_uprates', 'Directory\OrderUprateChildController');
    $router->resource('/directory_parities_uprates', 'Directory\ParitiesUprateController');
    $router->resource('/directory_parities_uprates/{scheme_name}/directory_parities_uprates', 'Directory\ParitiesUprateChildController');
    $router->resource('/directory_shell_products', 'Directory\ShellProductController');
    $router->resource('/directory_local_transactions', 'Directory\LocalTransactionsController');
    $router->resource('/directory_main_body', 'Directory\ChannelMainBodyController');
    $router->resource('/directory_bin', 'Directory\DirectoryBinbaseController');

    // 导出
    $router->get('/download/{file_name}', function ($fileName) {
        [$downloadFileName, $fileName] = explode('/', base64_decode($fileName));
        $file = config('excel.temporary_files.local_path') . '/' . $fileName;

        return response()->download($file, $downloadFileName);
    });

    // 风险管理
    $router->resource('/risk/channel_route_schemes', 'Risk\ChannelRouteScheme\SchemeController')->only(
        ['index', 'create', 'store']
    );
    $router->resource('/risk/channel_route_schemes/{scheme}/records', 'Risk\ChannelRouteScheme\RecordController')->only(
        ['index', 'create', 'store']
    );
    $router->delete('/risk/channel_route_schemes/{scheme}/records/{recordId}', 'Risk\ChannelRouteScheme\RecordController@delete')->name('channel.route.scheme.record.delete');
    $router->resource('/risk/channel_route_schemes/{scheme}/records/{record}/s2s', 'Risk\ChannelRouteScheme\S2sController')->only(
        ['index', 'create', 'store']
    );
    $router->get('/risk/channel_route_schemes/{scheme}/records/{record}/s2s/{s2s}/group/region/{region}/cc_type/{ccType}/currency/{currency}', 'Risk\ChannelRouteScheme\S2sController@group')->name('channel.route.scheme.record.s2s.group.create');
    $router->put('/risk/channel_route_schemes/{scheme}/records/{record}/s2s/{s2s}/group/region/{region}/cc_type/{ccType}/currency/{currency}', 'Risk\ChannelRouteScheme\S2sController@groupUpdate')->name('channel.route.scheme.record.s2s.group.update');
    $router->delete('/risk/channel_route_schemes/{scheme}/records/{record}/s2s/region/{region}/cc_type/{ccType}/currency/{currency}', 'Risk\ChannelRouteScheme\S2sController@groupDelete')->name('channel.route.scheme.record.s2s.delete');

    $router->resource('/risk/channel_route_schemes/{scheme}/records/{record}/directchannel', 'Risk\ChannelRouteScheme\DirectchannelController')->only(
        ['index', 'create', 'store']
    );
    $router->get('/risk/channel_route_schemes/{scheme}/records/{record}/directchannel/{directchannel}/group/region/{region}/cc_type/{ccType}/currency/{currency}', 'Risk\ChannelRouteScheme\DirectchannelController@group')->name('channel.route.scheme.record.directchannel.group.create');
    $router->put('/risk/channel_route_schemes/{scheme}/records/{record}/directchannel/{directchannel}/group/region/{region}/cc_type/{ccType}/currency/{currency}', 'Risk\ChannelRouteScheme\DirectchannelController@groupUpdate')->name('channel.route.scheme.record.directchannel.group.update');
    $router->delete('/risk/channel_route_schemes/{scheme}/records/{record}/directchannel/region/{region}/cc_type/{ccType}/currency/{currency}', 'Risk\ChannelRouteScheme\DirectchannelController@groupDelete')->name('channel.route.scheme.record.directchannel.delete');

    //本地支付路由
    $router->resource('/risk/local_channel_route_schemes', 'Risk\LocalChannelRouteScheme\SchemeController')->only(
        ['index', 'create', 'store']
    );
    $router->resource('/risk/local_channel_route_schemes/{scheme}/records/{record}/local_directchannel', 'Risk\LocalChannelRouteScheme\DirectchannelController')->only(
        ['create', 'store']
    );
    $router->get('/risk/local_channel_route_schemes/{scheme}/records/{record}/directchannel/{directchannel}/transaction_type/{transaction_type}/group', 'Risk\LocalChannelRouteScheme\DirectchannelController@group')->name('local.channel.route.scheme.record.directchannel.group.create');
    $router->put('/risk/local_channel_route_schemes/{scheme}/records/{record}/directchannel/{directchannel}/group/transaction_type/{transaction_type}', 'Risk\LocalChannelRouteScheme\DirectchannelController@groupUpdate')->name('local.channel.route.scheme.record.directchannel.group.update');
    $router->delete('/risk/local_channel_route_schemes/{scheme}/records/{record}/directchannel/t_type/{t_type}', 'Risk\LocalChannelRouteScheme\DirectchannelController@groupDelete')->name('local.channel.route.scheme.record.directchannel.delete');
    $router->resource('/risk/local_channel_route_schemes/{scheme}/local_records', 'Risk\LocalChannelRouteScheme\RecordController')->only(
        ['index', 'create', 'store']
    );
    $router->delete('/risk/local_channel_route_schemes/{scheme}/records/{recordId}', 'Risk\LocalChannelRouteScheme\RecordController@delete')->name('local.channel.route.scheme.record.delete');

    $router->resource('/merchant_urls', 'Risk\Urls\UrlController')->middleware('change-locale');
    $router->resource('/risk/urls/config', 'Risk\Urls\ConfigController');
    $router->resource('/risk/dishonour_control_tasks', 'Risk\DishonourControl\TaskController');
    $router->resource('/risk/{dictionary_id}/subtasks', 'Risk\DishonourControl\SubtaskController');
    $router->resource('/risk/channel_route_preset', 'Risk\ChannelRoutePresetController');
    $router->post('/risk/channel_route_preset/route_page', 'Risk\ChannelRoutePresetController@routePage');
    $router->get('/risk/channel_route_preset/edits/{id}', 'Risk\ChannelRoutePresetController@group')->name('channel.route.preset.create');
    $router->put('/risk/channel_route_preset/update/{id}', 'Risk\ChannelRoutePresetController@groupUpdate')->name('channel.route.preset.update');
    $router->resource('/risk/risk_analysis', 'Risk\RiskAnalysisController');
    $router->resource('/risk/order_risk_transaction', 'Risk\OrderRiskTransactionController'); // 风险交易

    //卡渠道
    $router->resource('/card/supplier', 'CardBin\CardBinSupplierController');
    $router->resource('/card/bin_supplier_class', 'CardBin\CardBinClassController');
    $router->resource('/card/supplier_class/{bin_class_id}/card_bin', 'CardBin\CardBinController');
    $router->get('/card/supplier_class/bins_config/edits/{id}', 'CardBin\CardBinController@group')->name('card_bin.bins_config.edits');
    $router->put('/card/supplier_class/bins_config/updates/{id}', 'CardBin\CardBinController@groupUpdate')->name('card_bin.bins_config.updates');
    $router->resource('/card_virtual', 'CardVirtual\CardVirtualController')->middleware('cardVirtualAuditLog');
    $router->resource('/card_batch_virtual', 'CardVirtual\CardBatchController');
    $router->resource('/card_transactions', 'CardVirtual\CardTransactionsController')->middleware('cardVirtualAuditLog');
    $router->resource('/card_tickets', 'CardVirtual\MerchantCardTicketController');
    $router->resource('/card_settle_details', 'CardVirtual\SettleDetailCardController')->middleware('cardVirtualAuditLog');
    $router->resource('/card_destroy', 'CardVirtual\CardDestroyController')->middleware('cardVirtualAuditLog');
    $router->resource('/card_virtual_channel_logs', 'CardVirtual\CardVirtualChannelLogsController')->middleware('cardVirtualAuditLog');
    $router->resource('/settle_adjustment_cards', 'CardVirtual\SettleAdjustmentCardController')->middleware('cardVirtualAuditLog');
    $router->resource('/card/holder', 'CardVirtual\CardHolderController')->middleware('cardVirtualAuditLog');
    $router->resource('/card/entry_trans', 'CardVirtual\CardEntryTransController')->middleware('cardVirtualAuditLog');


    // 渠道管理
    $router->get('/channel_pids/channels/get_channels/{type}/{return?}', 'Channel\Pid\ChannelController@getChannels');
    $router->resource('/channel_pids/{channel_pid}/channels', 'Channel\Pid\ChannelController');
    $router->resource('/channel_pids/{channel_pid}/withdrawal', 'Channel\Pid\WithdrawalController')->names('withdrawal');
    $router->get('/channel_pids/get_pid/{channel_pid?}', 'Channel\PidController@getPid');
    $router->resource('/channel_pids', 'Channel\PidController');
    $router->resource('/channel_suppliers', 'Channel\SupplierController');
    $router->resource('/channel_suppliers/{file_name}/channel_external_codes', 'Channel\Supplier\ExternalCodeController');
    $router->resource('/channel_withdrawal', 'Channel\WithdrawalController');
    $router->post('/withdrawal/code', 'Channel\Pid\WithdrawalController@code');
    $router->resource('/channel_order_uprates/{channel_supplier_id}/channel_order_uprates', 'Channel\OrderUprateChildController');
    $router->resource('/channel_parities_uprates/{channel_supplier_id}/channel_parities_uprates', 'Channel\ParitiesUprateChildController');
    $router->resource('/channel_daily_bill', 'Channel\DailyBillController');
    $router->resource('/channel_merchants', 'Channel\ChannelMerchantController');

    // 商户管理
    $router->resource('/merchants', 'Merchant\MerchantController');
    $router->resource('/merchant_kyc', 'Merchant\MerchantKycController')->middleware('change-locale');
    $router->resource('/businesses', 'Merchant\BusinessController');
    $router->get('/businesses/{business}/edit/{merchant_id?}', 'Merchant\BusinessController@edit')->name('business.edit');
    $router->resource('/merchants/{merchant_id}/businesses', 'Merchant\BusinessController');
    $router->resource('/merchant_permissions', 'Merchant\PermissionController');
    $router->resource('/merchant_menus', 'Merchant\MenuController');
    $router->resource('/merchant_card', 'Merchant\MerchantCardController');
    $router->delete('/historyBusiness/{id}', 'Merchant\BusinessController@historyInvalid')->name('history.business.invalid.delete');

    // 争议管理
    $router->resource('/complaints', 'Dispute\ComplaintController');
    $router->get('/complaints/item/{id}', 'Dispute\ComplaintController@item')->name('complaints.item')->middleware('complaint');
    $router->post('/complaints/item/{id}', 'Dispute\ComplaintController@save')->name('complaints.save');
    $router->post('complaints/notice/{id}', 'Dispute\ComplaintController@notice')->name('complaints.notice');
    $router->resource('/chargeback', 'Dispute\ChargebackController');
    $router->resource('/alert_chargeback', 'Dispute\ChargebackCaseController');
    $router->resource('/alert_chargeback_abnormal', 'Dispute\ChargebackCaseAbnormalController');
    $router->post('/alert_chargeback_abnormal/check_order', 'Dispute\ChargebackCaseAbnormalController@checkOrder')->name('chargeback_abnormal.check_order');
    $router->resource('/alert_config', 'Dispute\ConfigController');
    $router->resource('/chargeback_penalty', 'Dispute\ChargebackPenaltyController');

    // 交易管理
    $router->resource('/tasks', 'Transaction\TaskController');
    $router->resource('/orders', 'Transaction\OrderController')->names('admin_orders');
    $router->resource('/refunds', 'Transaction\RefundApplyController');
    $router->resource('/info_refunds', 'Transaction\RefundController');
    $router->resource('/errors', 'Transaction\ErrorController');
    $router->resource('/refund_errors', 'Transaction\RefundErrorController');
    $router->resource('/order_loses', 'Transaction\OrderLosesController');
    $router->resource('/data_statistics', 'Transaction\DataStatisticsController');
    $router->resource('/abnormal_refund', 'Transaction\AbnormalRefundController');

    // 卡库管理
    $router->resource('/cards', 'Card\SourceController');
    $router->resource('/card_info', 'Card\InfoController');

    // 运单管理
    $router->resource('/track/tracks', 'Track\TrackController');
    $router->resource('/track/orders', 'Track\OrderController');
    $router->resource('/track/libraries', 'Track\LibraryController');

    // 邮件管理
    $router->resource('/email/tasks', 'Email\TaskController');

    // 结算管理
    $router->resource('/settlement/order', 'Settlement\OrderController');
    $router->resource('/settlement/adjustment', 'Settlement\AdjustmentController');
    $router->resource('/settlement/detail', 'Settlement\DetailController');
    $router->resource('/settlement/settlement', 'Settlement\SettlementController');
    $router->resource('/settlement/refill', 'Settlement\RefillController');
    $router->resource('/settlement/particular_merchants', 'Settlement\DetailMerchantController');
    $router->get('/settlement/merchant_currency', 'Settlement\MerchantRegularDepositController@getMerchantCurrency');
    $router->post('/settlement/business_deposit', 'Settlement\MerchantRegularDepositController@getBusinessDeposit');
    $router->resource('/settlement/regular_deposit', 'Settlement\MerchantRegularDepositController');
    $router->resource('/settlement/merchant_report_forms', 'Settlement\MerchantReportFormController');
    $router->resource('/settlement/frozen', 'Settlement\MerchantRiskFrozenOperationController');

    // 提现管理
    $router->resource('/transfer/tickets', 'Transfer\TicketController');
    $router->resource('/transfer/checkTickets', 'Transfer\CheckTicketController');
    $router->resource('/transfer/processTickets', 'Transfer\ProcessTicketController');
    $router->resource('/transfer/reviewTickets', 'Transfer\ReviewTicketController');
    $router->resource('/transfer/account', 'Transfer\AccountController');

    //风控管理
    $router->resource('/risk_control/business_risk', 'RiskControl\MerchantControl\BusinessRiskController');
    $router->resource('/card/card_white_list', 'RiskControl\Card\CardWhiteListController');
    $router->resource('/card/{merchant_id}/card_white_list', 'RiskControl\Card\CardWhiteListController');
    $router->resource('/risk_control/blacklist', 'RiskControl\MerchantControl\BlacklistController');
    $router->resource('/risk_control/{merchant_id}/blacklist', 'RiskControl\MerchantControl\BlacklistController');
    $router->resource('/risk_control/sys_notice', 'RiskControl\Notice\SysNoticeController');
    $router->resource('/card/channel_white_list', 'RiskControl\Card\ChannelWhiteListedController');
    $router->resource('/risk_control/channel_risk', 'RiskControl\Channel\ChannelRiskSpecialController');
    $router->resource('/card/card_white_list_by_mid', 'RiskControl\Card\CardWhiteListByMidCsvController');
    $router->resource('/card/card_cipher_whitelist', 'RiskControl\Card\CardCipherWhitelistController');
    $router->resource('/risk_control/cases', 'RiskControl\Cases\RiskCaseController')->middleware('change-locale');
    $router->resource('/risk_control/lexis_nexis/index', 'RiskControl\LexisNexis\SearchLexisNexisController')->middleware('change-locale');
    $router->get('/risk_control/lexis_nexis', 'RiskControl\LexisNexis\LexisNexisController@index')->middleware('change-locale');
    $router->get('/risk_control/lexis_nexis/search', 'RiskControl\LexisNexis\LexisNexisController@search')->middleware('change-locale');
    $router->post('/risk_control/lexis_nexis/store', 'RiskControl\LexisNexis\LexisNexisController@store')->middleware('change-locale');
    $router->get('/risk_control/lexis_nexis_scan', 'RiskControl\LexisNexis\LexisNexisScanController@index')->middleware('change-locale');
    $router->resource('/risk_control/mcc', 'RiskControl\Mcc\RiskMccController')->middleware('change-locale');
    $router->resource('/risk_summary', 'RiskControl\Summary\RiskSummaryController')->middleware('change-locale');
    $router->resource('/risk_control/merchant', 'RiskControl\MerchantControl\MerchantController')->middleware('change-locale');
    $router->post('/risk_control/risk_credit_assessments/process_mcc_table', 'RiskControl\CreditAssessment\CreditAssessmentController@processMccTable');
    $router->resource('/risk_credit_assessments', 'RiskControl\CreditAssessment\CreditAssessmentController')->middleware('change-locale');
    $router->get('/processing_snapshot/{id}', 'RiskControl\CreditAssessment\ProcessingSnapshotController@show')->middleware('change-locale');
    $router->resource('/risk_financial', 'RiskControl\Financial\RiskFinancialController')->middleware('change-locale');
    $router->get('/risk_financial/{merchant_id}/create', 'RiskControl\Financial\RiskFinancialController@create')->name('risk_financial.create')->middleware('change-locale');
    $router->resource('/risk_control/rules', 'RiskControl\Rules\RiskRuleController')->middleware('change-locale');

    // API管理
    $router->resource('/api/info', 'Api\ApiInfoController');
    $router->resource('/api/log', 'Api\ApiLogController');
    $router->resource('/api/allocation', 'Api\ApiAllocationController');
    $router->resource('/api/notice', 'Api\ApiNoticeTaskController');

    // 下载管理
    $router->resource('/download_control/download_center', 'Download\DownloadCenterController');

    // 表格管理
    $router->resource('/table_chargeback_stat', 'Table\ChargebackStatController');
    $router->resource('/table_transaction_stat', 'Table\TransactionStatController');
    $router->resource('table_success_stat', 'Table\SuccessStatController');

    // 上传管理
    $router->resource('/upload/centers', 'Upload\UploadCenterController');

    //店匠管理
    $router->resource('/shoplazza/post', 'Shoplazza\PostController');
    $router->resource('/shoplazza/store', 'Shoplazza\StoreController');
    $router->resource('/shoplazza/config', 'Shoplazza\ConfigController');

    // 管理员
    $router->resource('auth/users', 'UsersController');

    //本地支付交易管理
    $router->resource('/local_pay/orders', 'Transaction\LocalPay\OrderController');
    $router->resource('/local_pay/refunds', 'Transaction\LocalPay\RefundApplyController');
    $router->resource('/local_pay/info_refunds', 'Transaction\LocalPay\RefundController');
    $router->resource('/local_pay/errors', 'Transaction\LocalPay\ErrorController');
    $router->resource('/local_pay/refund_errors', 'Transaction\LocalPay\RefundErrorController');

    // 共享文件
    $router->resource('shared_files', 'SharedFileController');

    // OpenIm管理
    $router->resource('/openIm/user', 'OpenIm\OpenImUserController');

    // 虚拟卡风控管理
    $router->resource('/risk/while_list', 'CardVirtualRisk\CardVirtualWhiteController');
    $router->resource('/risk/black_list', 'CardVirtualRisk\CardVirtualBlackController');
    $router->resource('/risk/black_trade_list', 'CardVirtualRisk\BlackTradeController');
    $router->resource('/risk/fraud_trade_list', 'CardVirtualRisk\FraudTradeController');
});

// iframe-tab 解决多应用后台的路由问题
Route::group([
                 'prefix'     => config('admin.route.prefix'),
                 'middleware' => config('admin.route.middleware'),
             ], function (Router $router) {
    $controller = IframeController::class;
    $router->get('/', $controller . '@index');
});
