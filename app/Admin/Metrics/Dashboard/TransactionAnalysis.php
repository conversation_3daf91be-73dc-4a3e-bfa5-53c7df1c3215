<?php

namespace App\Admin\Metrics\Dashboard;

use App\Models\DirectoryCc;
use App\Models\DirectoryCountry;
use App\Models\DirectoryCurrency;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl;
use App\Models\RiskMcc;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Box;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class TransactionAnalysis
{

    public function handle()
    {
        $merchantId = Auth::user()->merchant_id;

        $bidList = MerchantBusiness::where("merchant_id", "=", $merchantId)->pluck('business_id')->toArray();
        $bidList = Arr::prepend($bidList, admin_trans_field('全部'));

        $bid = [
            "id"      => "bid",
            "options" => $bidList
        ];

        $bidMenu = TranDropdown::make($bid)->button('business_id')->click()
            ->map(function ($v, $k) {
                return "<a class='tran-switch-bar-bid' data-tranbid='{$k}'>{$v}</a>";
            });

        $merUrlList = MerchantUrl::where("merchant_id", "=", $merchantId)->pluck('url_name', 'id')->toArray();
        $merUrlList = [-1 => admin_trans_field('搜索'), 0 => admin_trans_field('全部')] + $merUrlList;

        $mer = [
            "id"      => "url",
            "options" => $merUrlList
        ];

        $merUrlMenu = TranDropdown::make($mer)->button('url_name')->click()
            ->map(function ($v, $k) {
                if ($v == admin_trans_field('搜索')){
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-url" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='tran-switch-bar-url' data-tranurl='{$k}'>{$v}</a>";
            });

        $dirList = RiskMcc::query()->where('overall_risk_rating', '!=', 'Prohibited')->pluck('mcc', 'id');
        $dirList = [admin_trans_field('全部')] + $dirList;

        $dir = [
            "id"      => "mcc",
            "options" => $dirList
        ];

        $dirMenu = TranDropdown::make($dir)->button('MCC')->click()
            ->map(function ($v, $k) {
                return "<a class='tran-switch-bar-mcc' data-tranmcc='{$k}'>{$v}</a>";
            });

        $dirCurrList = DirectoryCurrency::pluck('code')->toArray();
        $dirCurrList = Arr::prepend($dirCurrList, admin_trans_field('全部'));
        $dirCurrList = Arr::prepend($dirCurrList, admin_trans_field('搜索'));

        $dirCurr = [
            "id"      => "curr",
            "options" => $dirCurrList
        ];

        $dirCurrMenu = TranDropdown::make($dirCurr)->button('currency')->click()
            ->map(function ($v, $k) {
                if ($k == admin_trans_field('搜索')){
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-curr" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='tran-switch-bar-curr' data-trancurr='{$k}'>{$v}</a>";
            });

        $dirCcsList = DirectoryCc::isRiskControl()->pluck('cc_type')->toArray();
        $dirCcsList = Arr::prepend($dirCcsList, admin_trans_field('全部'));

        $dirCcs = [
            "id"      => "ccs",
            "options" => $dirCcsList
        ];

        $dirCcsMenu = TranDropdown::make($dirCcs)->button('cc_type')->click()
            ->map(function ($v, $k) {
                return "<a class='tran-switch-bar-ccs' data-tranccs='{$k}'>{$v}</a>";
            });

        $CountryList = DirectoryCountry::pluck('name')->toArray();
        $CountryList = Arr::prepend($CountryList, admin_trans_field('全部'));
        $CountryList = Arr::prepend($CountryList, admin_trans_field('搜索'));

        $Country = [
            "id"      => "country",
            "options" => $CountryList
        ];

        $countryMenu = TranDropdown::make($Country)->button('card_country')->click()
            ->map(function ($v, $k) {
                if ($k == admin_trans_field('搜索')){
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-country" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='tran-switch-bar-country' data-trancountry='{$k}'>{$v}</a>";
            });

        $transactionBox = TransactionBox::make([
            "bid"     => $bidList,
            "url"     => $merUrlList,
            "mcc"     => $dirList,
            "ccs"     => $dirCcsList,
            "curr"    => $dirCurrList,
            "country" => $countryMenu
        ])
            ->title(admin_trans_field('交易分析'))
            ->fetching('$("#tran-box").loading()')
            ->fetched('$("#tran-box").loading(false)')
            ->fetched(self::JS())
            ->click(['.tran-switch-bar-bid', '.date-submit', '.tran-switch-bar-url', '.tran-switch-bar-ccs', '.tran-switch-bar-mcc', '.tran-switch-bar-curr', '.tran-switch-bar-country'])
            ->id("tran-chart-box");

        $box             = Box::make()->title(admin_trans_field('交易分析'))->id("tran-box");
        $firstendDate    = date("Y/m/d");
        // 针对kiosk商户特殊处理
        if ($merchantId == '164791825957292') {
            $firstStartDate  = date("Y/m/d", strtotime(date("Y-m-d") . " -1 day"));
            $secondendDate   = date("Y/m/d", strtotime(date("Y-m-d") . " -1 day"));
            $secondStartDate = date("Y/m/d", strtotime(date("Y-m-d") . " -2 day"));
        } else {
            $firstStartDate  = date("Y/m/d", strtotime(date("Y-m-d") . " -15 day"));
            $secondendDate   = date("Y/m/d", strtotime(date("Y-m-d") . " -15 day"));
            $secondStartDate = date("Y/m/d", strtotime(date("Y-m-d") . " -30 day")); 
        }

        $buttonName = admin_trans_field('提交');

        Admin::js('vendor/dcat-admin/dcat/plugins/moment/moment-with-locales.min.js');
        Admin::js('vendor/dcat-admin/dcat/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js');
        Admin::css('vendor/dcat-admin/dcat/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css');
        Admin::script(
            '$("#firstDate1").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });
             $("#firstDate2").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });
             $("#secondDate1").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });
             $("#secondDate2").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });'
        );

        $box->content(
            <<<HTML
<div style="margin: 15px;">
    <form action="/dcat-api/value" method="post" class="fist-date" id="tran-date">
        <div class="row">
            <input id="firstDate1" class="firstDate1 form-control col-lg-1"  type="text" value="{$firstStartDate}">
            <div style="padding: 8px">-</div>
            <input id="firstDate2" class="firstDate2 form-control col-lg-1"  type="text" value="{$firstendDate}">
            <div style="padding: 9px">VS</div>
            <input id="secondDate1" class="secondDate1 form-control col-lg-1"  type="text" value="{$secondStartDate}">
            <div style="padding: 8px">-</div>
            <input id="secondDate2" class="secondDate2 form-control col-lg-1"  type="text" value="{$secondendDate}">
            &nbsp;
            {$bidMenu->render()}
            &nbsp;
            {$merUrlMenu->render()}
            &nbsp;
            {$dirMenu->render()}
            &nbsp;
            {$dirCurrMenu->render()}
            &nbsp;
            {$dirCcsMenu->render()}
            &nbsp;
            {$countryMenu->render()}
            <div class="col-lg-1">
                <button class="date-submit btn btn-primary btn-outline" type="button">{$buttonName}</button>
            </div>

        </div>
    </form>
</div>
{$transactionBox->render()}
HTML
        );

        return $box;
    }

    private static function JS() {
        $JS =  <<<JS
        $(".dropdown-menu").css({"height" : "350px", "overflow-y" : "auto"})
        let aClass  = ['tran-switch-bar-curr', 'tran-switch-bar-country', 'tran-switch-bar-url'];
        let aId     = ['curr', 'country', 'url']
        let aSearch = ['transaction-drop-search-curr', 'transaction-drop-search-country', 'transaction-drop-search-url']
        for(let i = 0; i < aSearch.length; i++) {
            let child = $("#"+aId[i]).next().find('.'+aClass[i])
            child.parent().css("display","block");
            $("#"+aSearch[i]).click(function () {
                event.stopPropagation();
            }).bind("input propertychange", function() {
                for(let j = 0; j < child.length; j++) {
                    if (child[j].text.toLowerCase().indexOf($(this).val().toLowerCase()) > -1) {
                        child[j].parentNode.style.display = "block";
                    } else {
                        child[j].parentNode.style.display = "none";
                    }
                }
             }).val("").parent().click(function() {
                 event.stopPropagation();
             }).parent().css("padding",'0').removeAttr("href");
        }
JS;
        return $JS;
    }
}
